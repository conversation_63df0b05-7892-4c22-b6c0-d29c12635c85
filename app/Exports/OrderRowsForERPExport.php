<?php

namespace App\Exports;

use App\Models\Order\Order;
use App\Models\Order\OrderRow;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderRowsForERPExport implements FromQuery, WithHeadings, WithMapping
{
    protected $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
    
    public function query()
    {
        // Get cart group IDs associated with this order
        $cartGroupIds = $this->order->cartGroups()->pluck('id')->toArray();

        // Filter order rows by these cart group IDs with necessary relationships
        return OrderRow::query()
            ->with(['product'])
            ->whereIn('cart_group_id', $cartGroupIds)
            ->orderBy('sort');
    }

    public function headings(): array
    {
        return [
            'ergo_id',
            'row_number',
            'date',
            'description',
            'order_code',
            'ergo_partner_id',
            'adhoc_partner_id',
            'payment_term',
            'vat_type',
            'ergo_invoicing_address_id',
            'adhoc_invoicing_address_id',
            'ergo_shipping_address_id',
            'adhoc_shipping_address_id',
            'position_id',
            'adhoc_sku',
            'quantity',
            'price',
            'discount',
            'variation',
            'required_delivery_date',
            'adhoc_order_code',
            'note',
            'row_type',
            'bank_account',
        ];
    }

    public function map($orderRow): array
    {
        static $rowNumber = 0;
        $rowNumber++;

        return [
            $this->order->id, // ergo_id
            $rowNumber, // row_number
            $this->order->date?->format('d-m-Y'), // date
            $orderRow->description, // description
            $this->order->order_code, // order_code
            $this->order->partner_id, // ergo_partner_id
            '', // adhoc_partner_id (not available)
            $this->order->paymentTerm?->code, // payment_term
            $this->order->vat_type?->adhocCode(), // vat_type
            $this->order->invoicing_address_id, // ergo_invoicing_address_id
            $this->order->invoicingAddress?->code_invoicing, // adhoc_invoicing_address_id
            $this->order->shipping_address_id, // ergo_shipping_address_id
            $this->order->shippingAddress?->code_shipping, // adhoc_shipping_address_id
            $orderRow->position_id, // position_id
            $orderRow->product?->adhoc_sku ?? '', // adhoc_sku
            $orderRow->quantity, // quantity
            number_format($orderRow->selling_price, 2, '.', ''), // price
            $orderRow->discount ? '-' . $orderRow->discount : '', // discount
            $orderRow->variation ? (string) $orderRow->variation : '', // variation
            $orderRow->required_delivery_date?->format('d-m-Y'), // required_delivery_date
            $this->order->order_code_type->getAdhocCodeFromString($this->order->order_code_type->value), // adhoc_order_code
            '', // note (not available)
            'X', // row_type
            'BCC', // bank_account
        ];
    }
}
